# Cloudflare Pages Configuration
# This file configures the Cloudflare Pages deployment

name = "yugioh-card-maker"
compatibility_date = "2024-01-01"

# Pages configuration
[env.production]
compatibility_date = "2024-01-01"

# Build configuration
[build]
command = "npm run generate"
cwd = "."
watch_dir = "."

# Environment variables (set these in Cloudflare dashboard)
[vars]
NODE_ENV = "production"
DEPLOY_ENV = "CLOUDFLARE_PAGES"

# Custom domains (configure in Cloudflare dashboard)
# routes = [
#   { pattern = "yugiohcardmaker.org", custom_domain = true },
#   { pattern = "www.yugiohcardmaker.org", custom_domain = true }
# ]
