# Cloudflare Pages 部署指南

## 📋 概述

本指南详细说明如何将遊戲王卡片製造機部署到 Cloudflare Pages。Cloudflare Pages 提供了出色的性能、全球 CDN 和免费的 SSL 证书。

## 🚀 快速开始

### 1. 准备工作

确保你的项目已经推送到 Git 仓库（GitHub、GitLab 或 Bitbucket）。

### 2. 连接到 Cloudflare Pages

1. 登录 [Cloudflare Dashboard](https://dash.cloudflare.com/)
2. 选择 "Pages" 选项卡
3. 点击 "Create a project"
4. 选择 "Connect to Git"
5. 授权并选择你的仓库

### 3. 配置构建设置

在 Cloudflare Pages 设置页面中配置以下参数：

```
Framework preset: None (或 Nuxt.js 如果可用)
Build command: npm run generate:cloudflare
Build output directory: dist
Root directory: / (保持默认)
```

### 4. 环境变量设置

在 Cloudflare Pages 项目设置中添加以下环境变量：

```
NODE_ENV=production
DEPLOY_ENV=CLOUDFLARE_PAGES
NODE_OPTIONS=--openssl-legacy-provider
NUXT_TELEMETRY_DISABLED=1
```

## ⚙️ 详细配置

### 构建命令说明

项目提供了多个构建命令：

- `npm run generate:cloudflare` - Cloudflare Pages 专用构建
- `npm run build:cloudflare` - Cloudflare Pages 专用构建（SSR模式）
- `npm run preview` - 本地预览构建结果

### 文件结构

部署相关的配置文件：

```
├── static/
│   ├── _redirects          # 路由重定向配置
│   └── _headers            # HTTP 头配置
├── wrangler.toml           # Cloudflare 配置
├── .env.cloudflare         # 环境变量模板
├── .nvmrc                  # Node.js 版本
└── nuxt.config.js          # Nuxt.js 配置（已优化）
```

### 路由配置

`static/_redirects` 文件处理 SPA 路由：

```
/*    /index.html   200
```

这确保所有路由都正确指向 index.html，让 Vue Router 处理客户端路由。

### 缓存配置

`static/_headers` 文件配置了优化的缓存策略：

- HTML 文件：不缓存
- JS/CSS 文件：长期缓存（1年）
- 图片/字体：长期缓存（1年）
- 数据文件：短期缓存（1小时）

## 🔧 高级配置

### 自定义域名

1. 在 Cloudflare Pages 项目中点击 "Custom domains"
2. 添加你的域名
3. 按照指示配置 DNS 记录

### 分支部署

Cloudflare Pages 支持分支部署：

- `main/master` 分支 → 生产环境
- 其他分支 → 预览环境

### 环境变量管理

可以为不同环境设置不同的环境变量：

- Production environment
- Preview environment

## 📊 性能优化

### 已实现的优化

1. **静态生成**：使用 `nuxt generate` 生成静态文件
2. **代码分割**：自动分割 JavaScript 包
3. **图片优化**：优化图片加载和缓存
4. **字体优化**：预加载关键字体
5. **CSS 提取**：提取和压缩 CSS

### Cloudflare 特定优化

1. **全球 CDN**：自动分发到全球边缘节点
2. **Brotli 压缩**：自动启用 Brotli 压缩
3. **HTTP/2**：自动启用 HTTP/2
4. **缓存优化**：智能缓存策略

## 🚨 故障排除

### 常见问题

1. **构建失败**
   - 检查 Node.js 版本（应为 18.x）
   - 确认所有依赖已正确安装
   - 检查环境变量设置

2. **路由不工作**
   - 确认 `_redirects` 文件在 `static` 目录中
   - 检查 SPA 回退配置

3. **资源加载失败**
   - 检查资源路径是否正确
   - 确认 `_headers` 文件配置正确

### 调试步骤

1. **本地测试**：
   ```bash
   npm run generate:cloudflare
   npm run preview
   ```

2. **检查构建日志**：
   在 Cloudflare Pages 部署页面查看详细日志

3. **检查网络请求**：
   使用浏览器开发者工具检查资源加载

## 📈 监控和分析

### Cloudflare Analytics

Cloudflare Pages 提供内置分析：
- 页面访问量
- 带宽使用
- 缓存命中率
- 错误率

### Web Vitals

项目已集成 Web Vitals 监控，可以在浏览器控制台查看性能指标。

## 🔄 持续部署

### 自动部署

推送到连接的 Git 分支会自动触发部署：

1. 推送代码到仓库
2. Cloudflare Pages 自动检测更改
3. 运行构建命令
4. 部署到全球 CDN

### 部署钩子

可以配置 webhook 来触发其他操作：
- 发送通知
- 更新其他服务
- 运行测试

## 📞 支持

如果遇到问题：

1. 查看 [Cloudflare Pages 文档](https://developers.cloudflare.com/pages/)
2. 检查项目的 GitHub Issues
3. 联系项目维护者

---

## ✅ 部署检查清单

### 部署前检查

- [ ] 代码已推送到 Git 仓库
- [ ] 本地构建测试通过：`npm run generate:cloudflare`
- [ ] 所有配置文件已创建：`_redirects`, `_headers`, `wrangler.toml`
- [ ] 环境变量已准备好

### Cloudflare Pages 设置

- [ ] 项目已连接到 Git 仓库
- [ ] 构建命令设置为：`npm run generate:cloudflare`
- [ ] 输出目录设置为：`dist`
- [ ] 环境变量已配置
- [ ] 自定义域名已设置（如需要）

### 部署后验证

- [ ] 网站可以正常访问
- [ ] 所有页面路由工作正常
- [ ] 静态资源（图片、字体、CSS）加载正常
- [ ] 卡片制作功能正常工作
- [ ] 语言切换功能正常
- [ ] 移动端响应式正常

**部署成功后，你的遊戲王卡片製造機将在全球范围内快速访问！** 🎉
