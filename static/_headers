# Cloudflare Pages Headers Configuration
# This file configures caching, security, and performance headers

# Default headers for all files
/*
  X-Frame-Options: DENY
  X-Content-Type-Options: nosniff
  X-XSS-Protection: 1; mode=block
  Referrer-Policy: strict-origin-when-cross-origin
  Permissions-Policy: camera=(), microphone=(), geolocation=()

# Main HTML file - no cache for SPA
/index.html
  Cache-Control: no-cache, no-store, must-revalidate
  Pragma: no-cache
  Expires: 0

# JavaScript and CSS files - long cache with versioning
/_nuxt/*
  Cache-Control: public, max-age=31536000, immutable
  
# Static assets - long cache
/images/*
  Cache-Control: public, max-age=31536000
  
/fonts/*
  Cache-Control: public, max-age=31536000
  
/css/*
  Cache-Control: public, max-age=31536000
  
# Favicon and manifest files
/favicon.ico
  Cache-Control: public, max-age=86400
  
/site.webmanifest
  Cache-Control: public, max-age=86400
  
# Language files - medium cache
/lang.*.json
  Cache-Control: public, max-age=3600
  
# YGO data files - medium cache  
/ygo/*
  Cache-Control: public, max-age=3600

# Service Worker - no cache
/sw.js
  Cache-Control: no-cache, no-store, must-revalidate
  Service-Worker-Allowed: /

# Security headers for API endpoints (if any)
/api/*
  X-Robots-Tag: noindex
  Cache-Control: no-store
