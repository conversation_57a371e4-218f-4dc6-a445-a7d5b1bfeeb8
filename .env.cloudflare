# Cloudflare Pages Environment Configuration
# This file contains environment variables specific to Cloudflare Pages deployment

# Deployment environment
NODE_ENV=production
DEPLOY_ENV=CLOUDFLARE_PAGES

# Build configuration
NODE_OPTIONS=--openssl-legacy-provider

# Base URL (will be set by Cloudflare Pages automatically)
# BASE_URL will be provided by Cloudflare Pages context

# Analytics (configure in Cloudflare dashboard if needed)
# GA_TRACKING_ID=G-WWZYYWN8W7

# Performance optimizations
NUXT_TELEMETRY_DISABLED=1

# Build optimizations
GENERATE_SOURCEMAP=false
MINIMIZE=true
